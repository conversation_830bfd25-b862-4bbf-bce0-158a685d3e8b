/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<8fb462af82cf9a0842ced8a65e52034e>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Components/Switch/SwitchNativeComponent.js
 */

export * from "../../../src/private/specs_DEPRECATED/components/SwitchNativeComponent";
import SwitchNativeComponent from "../../../src/private/specs_DEPRECATED/components/SwitchNativeComponent";
declare const $$SwitchNativeComponent: typeof SwitchNativeComponent;
declare type $$SwitchNativeComponent = typeof $$SwitchNativeComponent;
export default $$SwitchNativeComponent;
