{"name": "lansia-health-server", "version": "1.0.0", "description": "Backend API for Aplikasi Kesehatan Lansia", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["health", "elderly", "posyandu", "qr-code", "api"], "author": "<PERSON>", "license": "MIT", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "expo": "^53.0.20", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.14.3", "qrcode": "^1.5.4"}, "devDependencies": {"nodemon": "^3.1.10"}, "engines": {"node": ">=18.0.0"}}