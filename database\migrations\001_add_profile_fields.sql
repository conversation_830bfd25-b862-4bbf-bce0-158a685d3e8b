-- =====================================================
-- Migration: Add additional fields to profiles table
-- Date: 2024-07-30
-- Description: Add phone numbers and medication fields
-- =====================================================

USE app_lansia;

-- Add new columns to profiles table
ALTER TABLE profiles 
ADD COLUMN no_telepon VARCHAR(20) NULL AFTER alamat,
ADD COLUMN kontak_darurat VARCHAR(20) NULL AFTER no_telepon,
ADD COLUMN obat_rutin TEXT NULL AFTER riwayat_medis,
ADD COLUMN alergi TEXT NULL AFTER obat_rutin;

-- Add indexes for the new fields
ALTER TABLE profiles 
ADD INDEX idx_no_telepon (no_telepon),
ADD INDEX idx_kontak_darurat (kontak_darurat);

-- Update the view to include new fields
DROP VIEW IF EXISTS profile_with_latest_checkup;

CREATE VIEW profile_with_latest_checkup AS
SELECT 
    p.*,
    c.tekanan_darah as latest_tekanan_darah,
    c.gula_darah as latest_gula_darah,
    c.tanggal as latest_checkup_date,
    c.catatan as latest_catatan
FROM profiles p
LEFT JOIN checkups c ON p.id = c.profile_id
WHERE c.id = (
    SELECT MAX(id) FROM checkups WHERE profile_id = p.id
);

-- Update sample data with new fields (optional)
UPDATE profiles SET 
    no_telepon = '081234567890',
    kontak_darurat = '081987654321',
    obat_rutin = 'Amlodipine 5mg (1x sehari)',
    alergi = NULL
WHERE id = 1;

UPDATE profiles SET 
    no_telepon = '082345678901',
    kontak_darurat = '082876543210',
    obat_rutin = 'Simvastatin 20mg (1x sehari)',
    alergi = 'Seafood'
WHERE id = 2;

UPDATE profiles SET 
    no_telepon = '083456789012',
    kontak_darurat = '083765432109',
    obat_rutin = 'Allopurinol 100mg (1x sehari)',
    alergi = NULL
WHERE id = 3;

-- Verify the changes
SELECT 'Migration completed successfully' as status;
DESCRIBE profiles;
