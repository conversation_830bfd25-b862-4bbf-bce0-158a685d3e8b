<?xml version="1.0" encoding="utf-8"?>
<resources>
  <style name="Theme" />
  <style name="Theme.Catalyst"/>
  <style name="Theme.Catalyst.RedBox">
    <item name="android:windowBackground">@color/catalyst_redbox_background</item>
    <item name="android:windowAnimationStyle">@style/Animation.Catalyst.RedBox</item>
    <item name="android:inAnimation">@android:anim/fade_in</item>
    <item name="android:outAnimation">@android:anim/fade_out</item>
    <item name="android:textColor">@android:color/white</item>
  </style>
  <style name="Theme.Catalyst.LogBox">
    <item name="android:windowTranslucentStatus">true</item>
    <item name="android:windowTranslucentNavigation">false</item>
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.Catalyst.LogBox</item>
    <item name="android:inAnimation">@android:anim/fade_in</item>
    <item name="android:outAnimation">@android:anim/fade_out</item>
    <item name="android:textColor">@android:color/white</item>
  </style>
  <style name="Animation.Catalyst.RedBox" parent="@android:style/Animation">
    <item name="android:windowEnterAnimation">@anim/catalyst_push_up_in</item>
    <item name="android:windowExitAnimation">@anim/catalyst_push_up_out</item>
  </style>
  <style name="Animation.Catalyst.LogBox" parent="@android:style/Animation">
    <item name="android:windowEnterAnimation">@anim/catalyst_push_up_in</item>
    <item name="android:windowExitAnimation">@anim/catalyst_push_up_out</item>
  </style>
  <style name="redboxButton">
    <item name="android:layout_width">0dp</item>
    <item name="android:layout_height">wrap_content</item>
    <item name="android:layout_weight">1</item>
    <item name="android:layout_margin">4dp</item>
    <item name="android:background">@null</item>
    <item name="android:gravity">center</item>
    <item name="android:textColor">#dddddd</item>
    <item name="android:textSize">14sp</item>
  </style>
  <style name="NoAnimationDialog" parent="Theme.AppCompat.Dialog">
    <item name="android:windowEnterAnimation">@null</item>
    <item name="android:windowExitAnimation">@null</item>
  </style>
</resources>
