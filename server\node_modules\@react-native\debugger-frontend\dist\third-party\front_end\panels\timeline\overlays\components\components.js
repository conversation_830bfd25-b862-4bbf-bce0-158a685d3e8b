import*as e from"../../../../ui/components/helpers/helpers.js";import*as t from"../../../../ui/lit-html/lit-html.js";import*as i from"../../../../core/i18n/i18n.js";import*as n from"../../../../models/trace/trace.js";const s=new CSSStyleSheet;s.replaceSync(".label-parts-wrapper{transform:translate(-50%);display:flex;flex-direction:column;color:var(--color-selected-option);align-items:center}.label-box{pointer-events:auto;background-color:var(--sys-color-token-variable);border-radius:4px;font-family:var(--monospace-font-family);font-weight:bold;white-space:nowrap}.label-box:focus{background-color:var(--color-selected-option);color:var(--sys-color-token-variable)}.connectorContainer{overflow:visible}.entry-highlight-wrapper{box-sizing:border-box;border:2px solid var(--sys-color-token-variable);border-radius:7px}\n/*# sourceURL=entryLabelOverlay.css */\n");class a extends Event{static eventName="emptyentrylabelremoveevent";constructor(){super(a.eventName)}}class l extends Event{newLabel;static eventName="entrylabelchangeevent";constructor(e){super(l.eventName),this.newLabel=e}}class r extends HTMLElement{static LABEL_AND_CONNECTOR_SHIFT_LENGTH=8;static LABEL_CONNECTOR_HEIGHT=7;static LABEL_HEIGHT=17;static LABEL_PADDING=4;static LABEL_AND_CONNECTOR_HEIGHT=r.LABEL_HEIGHT+2*r.LABEL_PADDING+r.LABEL_CONNECTOR_HEIGHT;static MAX_LABEL_LENGTH=100;static litTagName=t.literal`devtools-entry-label-overlay`;#e=this.attachShadow({mode:"open"});#t=this.#i.bind(this);#n=!0;#s=null;#a=null;#l=null;#r=null;#o=null;#h;#c;constructor(e,t){super(),this.#i(),this.#a=this.#e.querySelector(".label-parts-wrapper"),this.#l=this.#a?.querySelector(".label-box")??null,this.#o=this.#a?.querySelector(".connectorContainer")??null,this.#r=this.#a?.querySelector(".entry-highlight-wrapper")??null,this.#h=e,this.#c=t,this.#d(e),this.#b()}connectedCallback(){this.#e.adoptedStyleSheets=[s]}#p(){const e=this.#l?.textContent??"";e!==this.#h&&(this.#h=e,this.dispatchEvent(new l(this.#h)))}#y(e){if(!this.#l)return!1;return"Enter"===e.key||"Escape"===e.key?(this.#l.dispatchEvent(new FocusEvent("blur",{bubbles:!0})),!1):null!==this.#l.textContent&&this.#l.textContent.length<=r.MAX_LABEL_LENGTH||(!!["Backspace","Delete","ArrowLeft","ArrowRight"].includes(e.key)||(!(1!==e.key.length||!e.ctrlKey)||(e.preventDefault(),!1)))}#u(e){e.preventDefault();const t=e.clipboardData;if(!t||!this.#l)return;const i=t.getData("text"),n=(this.#l.textContent+i).slice(0,r.MAX_LABEL_LENGTH+1);this.#l.textContent=n;const s=window.getSelection(),a=document.createRange();a.selectNodeContents(this.#l),a.collapse(!1),s?.removeAllRanges(),s?.addRange(a)}set entryLabelParams(t){t.height===this.#s?.height&&t.width===this.#s?.width&&t.cutOffEntryHeight===this.#s?.cutOffEntryHeight||(this.#s=t,e.ScheduledRender.scheduleRender(this,this.#t),this.#g())}#b(){if(!this.#o)return void console.error("`connectorLineContainer` element is missing.");const e=this.#o.querySelector("line"),t=this.#o.querySelector("circle");e&&t?(this.#o.setAttribute("width",(2*r.LABEL_AND_CONNECTOR_SHIFT_LENGTH).toString()),this.#o.setAttribute("height",r.LABEL_CONNECTOR_HEIGHT.toString()),e.setAttribute("x1","0"),e.setAttribute("y1","0"),e.setAttribute("x2",r.LABEL_AND_CONNECTOR_SHIFT_LENGTH.toString()),e.setAttribute("y2",r.LABEL_CONNECTOR_HEIGHT.toString()),e.setAttribute("stroke","black"),e.setAttribute("stroke-width","2"),t.setAttribute("cx",r.LABEL_AND_CONNECTOR_SHIFT_LENGTH.toString()),t.setAttribute("cy",r.LABEL_CONNECTOR_HEIGHT.toString()),t.setAttribute("r","3"),t.setAttribute("fill","black")):console.error("Some entry label elements are missing.")}#d(e){this.#l?(this.#l.innerText=e,this.#l.style.height=`${r.LABEL_HEIGHT}px`,this.#l.style.padding=`${r.LABEL_PADDING}px`,this.#l.style.transform=`translateX(-${r.LABEL_AND_CONNECTOR_SHIFT_LENGTH}px)`,""!==e&&this.#v(!1)):console.error("`labelBox` element is missing.")}#g(){if(!(this.#r&&this.#s&&this.#l&&this.#o))return void console.error("Some elements required to draw `entryHighlightWrapper` are missing.");const{height:e,width:t,cutOffEntryHeight:i}=this.#s;this.#c?(this.#l.style.visibility=i>0?"hidden":"visible",this.#o.style.visibility=i>0?"hidden":"visible",this.#r.style.borderTopWidth=i>0?"0":"2px"):this.#r.style.borderBottomWidth=i>0?"0":"2px",this.#r.style.height=e-i+"px",this.#r.style.width=`${t}px`,this.#n&&this.#x()}#x(){this.#l?this.#l.focus():console.error("`labelBox` element is missing.")}#v(e){this.#n=e,this.#i(),e&&this.#x(),e||0!==this.#l?.innerText.length||this.dispatchEvent(new a)}#i(){t.render(t.html`
        <span class="label-parts-wrapper">
          <span
            class="label-box"
            @dblclick=${()=>this.#v(!0)}
            @blur=${()=>this.#v(!1)}
            @keydown=${this.#y}
            @paste=${this.#u}
            @keyup=${this.#p}
            contenteditable=${this.#n}>
          </span>
          <svg class="connectorContainer">
            <line/>
            <circle/>
          </svg>
          <div class="entry-highlight-wrapper"/>
        </span>`,this.#e,{host:this})}}customElements.define("devtools-entry-label-overlay",r);var o=Object.freeze({__proto__:null,EmptyEntryLabelRemoveEvent:a,EntryLabelChangeEvent:l,EntryLabelOverlay:r});const h=new CSSStyleSheet;h.replaceSync(":host{display:flex;overflow:hidden;flex-direction:column;justify-content:flex-end;width:100%;height:100%;box-sizing:border-box;padding-bottom:5px}.label{font-style:italic;text-align:center;box-sizing:border-box;width:max-content;max-width:250px;padding:0 3px;pointer-events:all;user-select:none;&.labelHidden{user-select:none;pointer-events:none;visibility:hidden}&.offScreenLeft{text-align:left}&.offScreenRight{text-align:right}}.label-text{text-overflow:ellipsis;max-height:40px;overflow:hidden;display:-webkit-box;white-space:break-spaces;-webkit-line-clamp:2;-webkit-box-orient:vertical}\n/*# sourceURL=timeRangeOverlay.css */\n");class c extends HTMLElement{static litTagName=t.literal`devtools-time-range-overlay`;#e=this.attachShadow({mode:"open"});#t=this.#i.bind(this);#m=null;#h="";#L=null;connectedCallback(){this.#e.adoptedStyleSheets=[h]}set canvasRect(t){this.#L=t,e.ScheduledRender.scheduleRender(this,this.#t)}set duration(t){t!==this.#m&&(this.#m=t,e.ScheduledRender.scheduleRender(this,this.#t))}set label(t){t!==this.#h&&(this.#h=t,e.ScheduledRender.scheduleRender(this,this.#t))}#f(e){if(!this.#L)return 0;const{x:t,width:i}=e,n=t+i,s=this.#L.x,a=this.#L.x+this.#L.width,l=Math.max(s,t);return Math.min(a,n)-l}afterOverlayUpdate(){const e=this.#e.querySelector(".label");if(!e)return;if(!this.#L)return;const t=this.getBoundingClientRect(),i=e.getBoundingClientRect(),n=this.#f(t)-9<=i.width-9;if(e.classList.toggle("labelHidden",n),n)return;const s=(t.width-i.width)/2,a=t.x+s<this.#L.x;e.classList.toggle("offScreenLeft",a);const l=this.#L.x+this.#L.width,r=t.x+s+i.width>l;if(e.classList.toggle("offScreenRight",r),a)e.style.marginLeft=`${Math.abs(this.#L.x-t.x)+9}px`;else if(r){const n=l-i.width-t.x;e.style.marginLeft=`${n}px`}else e.style.marginLeft=`${s}px`}#i(){const e=this.#m?i.TimeUtilities.formatMicroSecondsTime(this.#m):"";t.render(t.html`<span class="label" title=${this.#h}><span class="label-text">${this.#h}</span>${e}</span>`,this.#e,{host:this})}}customElements.define("devtools-time-range-overlay",c);var d=Object.freeze({__proto__:null,TimeRangeOverlay:c});const b=new CSSStyleSheet;b.replaceSync(":host{display:flex;overflow:hidden;flex-direction:row;justify-content:flex-end;width:100%;height:100%;box-sizing:border-box;padding-bottom:5px}.timespan-breakdown-overlay-section{border:solid;border-top-color:var(--sys-color-on-primary);border-left-width:thin;border-right-width:thin;border-bottom-width:thick;align-content:flex-end;text-align:center;overflow:hidden;text-overflow:ellipsis;background-image:linear-gradient(360deg,var(--sys-color-on-primary),transparent)}.timespan-breakdown-overlay-label{font-style:italic;text-align:center;box-sizing:border-box;width:max-content;max-width:200px;padding:0 3px;overflow:hidden;text-overflow:ellipsis;&.labelHidden{user-select:none;pointer-events:none;visibility:hidden}&.offScreenLeft{text-align:left}&.offScreenRight{text-align:right}}\n/*# sourceURL=timespanBreakdownOverlay.css */\n");class p extends HTMLElement{static litTagName=t.literal`devtools-timespan-breakdown-overlay`;static TIMESPAN_BREAKDOWN_OVERLAY_STAGGER_PX=5;#e=this.attachShadow({mode:"open"});#t=this.#i.bind(this);#L=null;#w=null;connectedCallback(){this.#e.adoptedStyleSheets=[b],this.#i()}set canvasRect(t){this.#L=t,e.ScheduledRender.scheduleRender(this,this.#t)}set sections(t){t!==this.#w&&(this.#w=t,e.ScheduledRender.scheduleRender(this,this.#t))}#f(e){if(!this.#L)return 0;const{x:t,width:i}=e,n=t+i,s=this.#L.x,a=this.#L.x+this.#L.width,l=Math.max(s,t);return Math.min(a,n)-l}afterOverlayUpdate(){const e=this.#e.querySelectorAll(".timespan-breakdown-overlay-section");if(!e)return;if(!this.#L)return;for(const t of e){const e=t.querySelector(".timespan-breakdown-overlay-label");if(!e)break;const i=this.getBoundingClientRect(),n=t.getBoundingClientRect(),s=e.getBoundingClientRect(),a=this.#f(i)-9<=s.width-9;if(e.classList.toggle("labelHidden",a),a)break;const l=(n.width-s.width)/2,r=n.x+l<this.#L.x;e.classList.toggle("offScreenLeft",r);const o=this.#L.x+this.#L.width,h=n.x+l+s.width>o;if(e.classList.toggle("offScreenRight",h),r)e.style.marginLeft=`${Math.abs(this.#L.x-n.x)+9}px`;else if(h){const t=o-s.width-n.x;e.style.marginLeft=`${t}px`}else e.style.marginLeft=`${l}px`}}renderSection(e){const s=n.Helpers.Timing.microSecondsToMilliseconds(e.bounds.range);return t.html`
      <div class="timespan-breakdown-overlay-section">
        <div class="timespan-breakdown-overlay-label">${e.label}
          <div>${i.TimeUtilities.preciseMillisToString(s,2)}</div>
        </div>
      </div>`}#i(){t.render(t.html`${this.#w?.map(this.renderSection)}`,this.#e,{host:this})}}customElements.define("devtools-timespan-breakdown-overlay",p);var y=Object.freeze({__proto__:null,TimespanBreakdownOverlay:p});export{o as EntryLabelOverlay,d as TimeRangeOverlay,y as TimespanBreakdownOverlay};
